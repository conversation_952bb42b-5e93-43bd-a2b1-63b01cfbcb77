/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background: white;
    border-radius: 12px;
    margin: 10px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 300px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.sidebar-header h1 {
    color: #495057;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

/* 侧边栏标签页 */
.sidebar-tabs {
    display: flex;
    margin-bottom: 15px;
    background: #e9ecef;
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.2s;
}

.tab-btn.active {
    background: white;
    color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    color: #495057;
}

.new-chat-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.new-chat-btn:hover {
    background: #0056b3;
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

/* 文件树样式 */
.file-tree {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.file-tree-header {
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    gap: 8px;
}

.file-action-btn {
    flex: 1;
    padding: 6px 8px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.2s;
}

.file-action-btn:hover {
    background: #218838;
}

.file-tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.2s;
    font-size: 13px;
}

.file-item:hover {
    background: #e9ecef;
}

.file-item.active {
    background: #007bff;
    color: white;
}

.file-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.file-item.folder {
    font-weight: 500;
}

.file-item.file {
    margin-left: 16px;
}

.conversation-item {
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid transparent;
}

.conversation-item:hover {
    background: #e3f2fd;
}

.conversation-item.active {
    border-color: #007bff;
    background: #e3f2fd;
}

.conversation-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.conversation-preview {
    font-size: 12px;
    color: #6c757d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-time {
    font-size: 11px;
    color: #adb5bd;
    margin-top: 4px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    color: #495057;
    font-size: 1.3rem;
}

.delete-btn {
    padding: 8px 12px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
}

.delete-btn:hover {
    background: #c82333;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

/* 欢迎消息 */
.welcome-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.welcome-content {
    text-align: center;
    color: #6c757d;
}

.welcome-icon {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 20px;
}

.welcome-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #495057;
}

.welcome-content p {
    margin-bottom: 8px;
}

.features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.feature i {
    font-size: 1.5rem;
    color: #007bff;
}

/* 消息样式 */
.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #007bff;
}

.message.assistant .message-avatar {
    background: #28a745;
}

.message-content {
    max-width: 70%;
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.user .message-content {
    background: #007bff;
    color: white;
}

.message-text {
    line-height: 1.5;
    white-space: pre-wrap;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    margin-top: 8px;
}

.message.user .message-time {
    color: rgba(255, 255, 255, 0.8);
}

/* 工具调用样式 */
.tool-calls {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.tool-call {
    margin-bottom: 8px;
    font-size: 12px;
}

.tool-call-name {
    font-weight: 600;
    color: #007bff;
}

.tool-call-result {
    margin-top: 4px;
    padding: 6px;
    background: #e9ecef;
    border-radius: 4px;
    font-family: monospace;
    font-size: 11px;
}

/* 输入区域 */
.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-input {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#message-input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
    transition: border-color 0.2s;
}

#message-input:focus {
    outline: none;
    border-color: #007bff;
}

.send-btn {
    padding: 12px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s;
}

.send-btn:hover:not(:disabled) {
    background: #0056b3;
}

.send-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #495057;
    font-size: 14px;
}

/* 编辑器界面样式 */
.editor-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.editor-tabs {
    display: flex;
    gap: 4px;
}

.editor-tab {
    padding: 8px 16px;
    background: white;
    border: 1px solid #e9ecef;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    font-size: 13px;
    color: #495057;
    transition: all 0.2s;
    position: relative;
}

.editor-tab.active {
    background: white;
    color: #007bff;
    border-color: #007bff;
    z-index: 1;
}

.editor-tab:hover:not(.active) {
    background: #f8f9fa;
}

.editor-tab .close-btn {
    margin-left: 8px;
    color: #6c757d;
    cursor: pointer;
    font-size: 12px;
}

.editor-tab .close-btn:hover {
    color: #dc3545;
}

.editor-actions {
    display: flex;
    gap: 8px;
}

.editor-action-btn {
    padding: 6px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.editor-action-btn:hover {
    background: #0056b3;
}

.editor-action-btn.success {
    background: #28a745;
}

.editor-action-btn.success:hover {
    background: #218838;
}

.editor-content {
    flex: 1;
    background: white;
    position: relative;
}

.editor-container {
    width: 100%;
    height: 100%;
}

/* Monaco Editor 自定义样式 */
.monaco-editor {
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
}

.monaco-editor .margin {
    background-color: #f8f9fa !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        margin: 0;
        border-radius: 0;
    }

    .sidebar {
        width: 250px;
    }

    .message-content {
        max-width: 85%;
    }

    .features {
        flex-direction: column;
        gap: 15px;
    }

    .editor-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .editor-tabs {
        overflow-x: auto;
    }
}

/* 滚动条样式 */
.conversations-list::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.conversations-list::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
