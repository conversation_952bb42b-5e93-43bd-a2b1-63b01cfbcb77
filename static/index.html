<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Agent - Web Interface</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Monaco Editor CSS -->
    <link rel="stylesheet" data-name="vs/editor/editor.main" href="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/editor/editor.main.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-robot"></i> Minimal Agent</h1>
                <div class="sidebar-tabs">
                    <button id="chat-tab" class="tab-btn active">
                        <i class="fas fa-comments"></i> 对话
                    </button>
                    <button id="editor-tab" class="tab-btn">
                        <i class="fas fa-code"></i> 编辑器
                    </button>
                </div>
                <button id="new-chat-btn" class="new-chat-btn">
                    <i class="fas fa-plus"></i> 新对话
                </button>
            </div>

            <!-- 对话列表 -->
            <div class="conversations-list" id="conversations-list">
                <!-- 对话列表将在这里动态加载 -->
            </div>

            <!-- 文件树 -->
            <div class="file-tree" id="file-tree" style="display: none;">
                <div class="file-tree-header">
                    <button id="new-file-btn" class="file-action-btn">
                        <i class="fas fa-file-plus"></i> 新建文件
                    </button>
                    <button id="new-folder-btn" class="file-action-btn">
                        <i class="fas fa-folder-plus"></i> 新建文件夹
                    </button>
                </div>
                <div class="file-tree-content" id="file-tree-content">
                    <!-- 文件树内容 -->
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 聊天界面 -->
            <div class="chat-interface" id="chat-interface">
                <div class="chat-header" id="chat-header">
                    <h2 id="chat-title">选择或创建一个对话</h2>
                    <div class="chat-actions">
                        <button id="delete-chat-btn" class="delete-btn" style="display: none;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="welcome-content">
                            <i class="fas fa-robot welcome-icon"></i>
                            <h3>欢迎使用 Minimal Agent</h3>
                            <p>这是一个基于 Rust 构建的智能 Agent 系统</p>
                            <p>支持工具调用、对话历史记录和代码编辑</p>
                            <div class="features">
                                <div class="feature">
                                    <i class="fas fa-tools"></i>
                                    <span>工具调用</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-history"></i>
                                    <span>历史记录</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>智能推理</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-code"></i>
                                    <span>代码编辑</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container" id="chat-input-container" style="display: none;">
                    <div class="chat-input">
                        <textarea
                            id="message-input"
                            placeholder="输入您的消息..."
                            rows="1"
                        ></textarea>
                        <button id="send-btn" class="send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 编辑器界面 -->
            <div class="editor-interface" id="editor-interface" style="display: none;">
                <div class="editor-header">
                    <div class="editor-tabs" id="editor-tabs">
                        <!-- 编辑器标签页 -->
                    </div>
                    <div class="editor-actions">
                        <button id="save-file-btn" class="editor-action-btn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                        <button id="run-code-btn" class="editor-action-btn success">
                            <i class="fas fa-play"></i> 运行
                        </button>
                    </div>
                </div>

                <div class="editor-content">
                    <div class="editor-container" id="editor-container">
                        <!-- Monaco Editor 将在这里初始化 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-robot fa-spin"></i>
            <p>Agent 正在思考...</p>
        </div>
    </div>

    <!-- Monaco Editor JS -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js"></script>
    <script src="script.js"></script>
</body>
</html>
