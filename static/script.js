class MinimalAgentApp {
    constructor() {
        this.currentConversationId = null;
        this.conversations = [];
        this.currentInterface = 'chat'; // 'chat' or 'editor'
        this.editor = null;
        this.openFiles = new Map(); // 存储打开的文件
        this.activeFile = null;
        this.fileTree = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConversations();
        this.initializeMonacoEditor();
        this.loadFileTree();
    }

    bindEvents() {
        // 界面切换标签
        document.getElementById('chat-tab').addEventListener('click', () => {
            this.switchInterface('chat');
        });

        document.getElementById('editor-tab').addEventListener('click', () => {
            this.switchInterface('editor');
        });

        // 新建对话按钮
        document.getElementById('new-chat-btn').addEventListener('click', () => {
            this.createNewConversation();
        });

        // 发送消息按钮
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // 删除对话按钮
        document.getElementById('delete-chat-btn').addEventListener('click', () => {
            this.deleteCurrentConversation();
        });

        // 输入框回车发送
        const messageInput = document.getElementById('message-input');
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight(messageInput);
        });

        // 编辑器相关事件
        document.getElementById('new-file-btn').addEventListener('click', () => {
            this.createNewFile();
        });

        document.getElementById('new-folder-btn').addEventListener('click', () => {
            this.createNewFolder();
        });

        document.getElementById('save-file-btn').addEventListener('click', () => {
            this.saveCurrentFile();
        });

        document.getElementById('run-code-btn').addEventListener('click', () => {
            this.runCurrentFile();
        });
    }

    adjustTextareaHeight(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    async loadConversations() {
        try {
            const response = await fetch('/api/conversations');
            const data = await response.json();
            
            if (data.success) {
                this.conversations = data.data;
                this.renderConversationsList();
            } else {
                console.error('Failed to load conversations:', data.error);
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    }

    renderConversationsList() {
        const listContainer = document.getElementById('conversations-list');
        
        if (this.conversations.length === 0) {
            listContainer.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #6c757d;">
                    <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <p>还没有对话</p>
                    <p style="font-size: 12px;">点击上方按钮创建新对话</p>
                </div>
            `;
            return;
        }

        listContainer.innerHTML = this.conversations.map(conv => `
            <div class="conversation-item ${conv.id === this.currentConversationId ? 'active' : ''}" 
                 data-id="${conv.id}">
                <div class="conversation-title">
                    ${conv.title || '新对话'}
                </div>
                <div class="conversation-preview">
                    ${conv.last_message || '暂无消息'}
                </div>
                <div class="conversation-time">
                    ${this.formatTime(conv.updated_at)}
                </div>
            </div>
        `).join('');

        // 绑定点击事件
        listContainer.querySelectorAll('.conversation-item').forEach(item => {
            item.addEventListener('click', () => {
                const conversationId = item.dataset.id;
                this.selectConversation(conversationId);
            });
        });
    }

    async createNewConversation() {
        try {
            const response = await fetch('/api/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: null
                })
            });

            const data = await response.json();
            
            if (data.success) {
                await this.loadConversations();
                this.selectConversation(data.data);
            } else {
                console.error('Failed to create conversation:', data.error);
                alert('创建对话失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating conversation:', error);
            alert('创建对话时发生错误');
        }
    }

    async selectConversation(conversationId) {
        this.currentConversationId = conversationId;
        
        // 更新UI状态
        this.updateChatHeader();
        this.showChatInput();
        
        // 重新渲染对话列表以更新选中状态
        this.renderConversationsList();
        
        // 加载消息历史
        await this.loadMessages();
    }

    updateChatHeader() {
        const conversation = this.conversations.find(c => c.id === this.currentConversationId);
        const chatTitle = document.getElementById('chat-title');
        const deleteBtn = document.getElementById('delete-chat-btn');
        
        if (conversation) {
            chatTitle.textContent = conversation.title || '新对话';
            deleteBtn.style.display = 'block';
        }
    }

    showChatInput() {
        document.getElementById('chat-input-container').style.display = 'block';
        document.querySelector('.welcome-message').style.display = 'none';
    }

    async loadMessages() {
        if (!this.currentConversationId) return;

        try {
            const response = await fetch(`/api/conversations/${this.currentConversationId}/messages`);
            const data = await response.json();
            
            if (data.success) {
                this.renderMessages(data.data);
            } else {
                console.error('Failed to load messages:', data.error);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    renderMessages(messages) {
        const messagesContainer = document.getElementById('chat-messages');
        
        if (messages.length === 0) {
            messagesContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <i class="fas fa-comment-dots" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <p>开始您的对话吧！</p>
                </div>
            `;
            return;
        }

        messagesContainer.innerHTML = messages.map(msg => this.renderMessage(msg)).join('');
        this.scrollToBottom();
    }

    renderMessage(message) {
        const isUser = message.role === 'user';
        const avatar = isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        let toolCallsHtml = '';
        if (message.tool_calls && message.tool_calls.length > 0) {
            toolCallsHtml = `
                <div class="tool-calls">
                    <strong>🔧 工具调用:</strong>
                    ${message.tool_calls.map(call => `
                        <div class="tool-call">
                            <span class="tool-call-name">${call.name}</span>
                            <div class="tool-call-result">${JSON.stringify(call.parameters, null, 2)}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        return `
            <div class="message ${message.role}">
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(message.content)}</div>
                    ${toolCallsHtml}
                    <div class="message-time">${this.formatTime(message.timestamp)}</div>
                </div>
            </div>
        `;
    }

    async sendMessage() {
        const input = document.getElementById('message-input');
        const content = input.value.trim();
        
        if (!content || !this.currentConversationId) return;

        // 清空输入框并禁用发送按钮
        input.value = '';
        this.adjustTextareaHeight(input);
        this.setLoading(true);

        try {
            // 立即显示用户消息
            this.addMessageToUI({
                id: Date.now().toString(),
                role: 'user',
                content: content,
                timestamp: new Date().toISOString()
            });

            const response = await fetch(`/api/conversations/${this.currentConversationId}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content
                })
            });

            const data = await response.json();
            
            if (data.success) {
                // 显示助手回复
                this.addMessageToUI(data.data);
                
                // 更新对话列表
                await this.loadConversations();
            } else {
                console.error('Failed to send message:', data.error);
                alert('发送消息失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('发送消息时发生错误');
        } finally {
            this.setLoading(false);
        }
    }

    addMessageToUI(message) {
        const messagesContainer = document.getElementById('chat-messages');
        
        // 如果是第一条消息，清除欢迎内容
        if (messagesContainer.querySelector('.welcome-message') || 
            messagesContainer.children.length === 0) {
            messagesContainer.innerHTML = '';
        }
        
        messagesContainer.insertAdjacentHTML('beforeend', this.renderMessage(message));
        this.scrollToBottom();
    }

    async deleteCurrentConversation() {
        if (!this.currentConversationId) return;

        if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/api/conversations/${this.currentConversationId}`, {
                method: 'DELETE'
            });

            const data = await response.json();
            
            if (data.success) {
                // 重置当前对话
                this.currentConversationId = null;
                
                // 重新加载对话列表
                await this.loadConversations();
                
                // 重置UI
                this.resetChatUI();
            } else {
                console.error('Failed to delete conversation:', data.error);
                alert('删除对话失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error deleting conversation:', error);
            alert('删除对话时发生错误');
        }
    }

    resetChatUI() {
        document.getElementById('chat-title').textContent = '选择或创建一个对话';
        document.getElementById('delete-chat-btn').style.display = 'none';
        document.getElementById('chat-input-container').style.display = 'none';
        
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-content">
                    <i class="fas fa-robot welcome-icon"></i>
                    <h3>欢迎使用 Minimal Agent</h3>
                    <p>这是一个基于 Rust 构建的智能 Agent 系统</p>
                    <p>支持工具调用和对话历史记录</p>
                    <div class="features">
                        <div class="feature">
                            <i class="fas fa-tools"></i>
                            <span>工具调用</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-history"></i>
                            <span>历史记录</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-brain"></i>
                            <span>智能推理</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setLoading(loading) {
        const overlay = document.getElementById('loading-overlay');
        const sendBtn = document.getElementById('send-btn');
        
        overlay.style.display = loading ? 'flex' : 'none';
        sendBtn.disabled = loading;
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 界面切换方法
    switchInterface(interfaceType) {
        this.currentInterface = interfaceType;

        // 更新标签状态
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`${interfaceType}-tab`).classList.add('active');

        // 切换界面显示
        if (interfaceType === 'chat') {
            document.getElementById('chat-interface').style.display = 'flex';
            document.getElementById('editor-interface').style.display = 'none';
            document.getElementById('conversations-list').style.display = 'block';
            document.getElementById('file-tree').style.display = 'none';
            document.getElementById('new-chat-btn').style.display = 'block';
        } else {
            document.getElementById('chat-interface').style.display = 'none';
            document.getElementById('editor-interface').style.display = 'flex';
            document.getElementById('conversations-list').style.display = 'none';
            document.getElementById('file-tree').style.display = 'block';
            document.getElementById('new-chat-btn').style.display = 'none';

            // 确保编辑器正确初始化
            if (this.editor) {
                setTimeout(() => this.editor.layout(), 100);
            }
        }
    }

    // Monaco Editor 初始化
    async initializeMonacoEditor() {
        if (typeof require === 'undefined') {
            // 等待 Monaco loader 加载
            await new Promise(resolve => {
                const checkLoader = () => {
                    if (typeof require !== 'undefined') {
                        resolve();
                    } else {
                        setTimeout(checkLoader, 100);
                    }
                };
                checkLoader();
            });
        }

        require.config({
            paths: {
                'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
            }
        });

        require(['vs/editor/editor.main'], () => {
            this.editor = monaco.editor.create(document.getElementById('editor-container'), {
                value: '// 欢迎使用 Minimal Agent 编辑器\n// 选择左侧文件开始编辑，或创建新文件\n',
                language: 'javascript',
                theme: 'vs-light',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                minimap: { enabled: true },
                wordWrap: 'on'
            });

            // 监听编辑器内容变化
            this.editor.onDidChangeModelContent(() => {
                if (this.activeFile) {
                    this.markFileAsModified(this.activeFile);
                }
            });
        });
    }

    // 文件树相关方法
    async loadFileTree() {
        try {
            const response = await fetch('/api/files');
            const data = await response.json();

            if (data.success) {
                this.fileTree = data.data;
                this.renderFileTree();
            } else {
                console.error('Failed to load file tree:', data.error);
            }
        } catch (error) {
            console.error('Error loading file tree:', error);
        }
    }

    renderFileTree() {
        const container = document.getElementById('file-tree-content');
        container.innerHTML = this.renderFileTreeItems(this.fileTree);

        // 绑定文件点击事件
        container.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', () => {
                const path = item.dataset.path;
                const isFolder = item.classList.contains('folder');

                if (isFolder) {
                    this.toggleFolder(item);
                } else {
                    this.openFile(path);
                }
            });
        });
    }

    renderFileTreeItems(items, level = 0) {
        return items.map(item => {
            const indent = 'margin-left: ' + (level * 16) + 'px;';
            const icon = item.type === 'folder' ? 'fas fa-folder' : 'fas fa-file';
            const className = `file-item ${item.type}`;

            return `
                <div class="${className}" data-path="${item.path}" style="${indent}">
                    <i class="${icon}"></i>
                    <span>${item.name}</span>
                </div>
                ${item.children ? this.renderFileTreeItems(item.children, level + 1) : ''}
            `;
        }).join('');
    }

    async openFile(path) {
        try {
            const response = await fetch(`/api/files/${encodeURIComponent(path)}`);
            const data = await response.json();

            if (data.success) {
                this.openFiles.set(path, {
                    content: data.data.content,
                    language: this.detectLanguage(path),
                    modified: false
                });

                this.activeFile = path;
                this.updateEditor();
                this.updateEditorTabs();
                this.updateFileTreeSelection();
            } else {
                console.error('Failed to open file:', data.error);
                alert('打开文件失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error opening file:', error);
            alert('打开文件时发生错误');
        }
    }

    updateEditor() {
        if (!this.editor || !this.activeFile) return;

        const fileData = this.openFiles.get(this.activeFile);
        if (fileData) {
            this.editor.setValue(fileData.content);
            monaco.editor.setModelLanguage(this.editor.getModel(), fileData.language);
        }
    }

    updateEditorTabs() {
        const tabsContainer = document.getElementById('editor-tabs');
        const tabs = Array.from(this.openFiles.keys()).map(path => {
            const fileName = path.split('/').pop();
            const fileData = this.openFiles.get(path);
            const isActive = path === this.activeFile;
            const isModified = fileData.modified;

            return `
                <div class="editor-tab ${isActive ? 'active' : ''}" data-path="${path}">
                    <span>${fileName}${isModified ? ' •' : ''}</span>
                    <span class="close-btn" data-path="${path}">×</span>
                </div>
            `;
        }).join('');

        tabsContainer.innerHTML = tabs;

        // 绑定标签事件
        tabsContainer.querySelectorAll('.editor-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                if (!e.target.classList.contains('close-btn')) {
                    this.activeFile = tab.dataset.path;
                    this.updateEditor();
                    this.updateEditorTabs();
                }
            });
        });

        tabsContainer.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeFile(btn.dataset.path);
            });
        });
    }

    detectLanguage(path) {
        const ext = path.split('.').pop().toLowerCase();
        const languageMap = {
            'js': 'javascript',
            'ts': 'typescript',
            'py': 'python',
            'rs': 'rust',
            'go': 'go',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'xml': 'xml',
            'md': 'markdown',
            'txt': 'plaintext'
        };
        return languageMap[ext] || 'plaintext';
    }

    async saveCurrentFile() {
        if (!this.activeFile || !this.editor) return;

        try {
            const content = this.editor.getValue();
            const response = await fetch(`/api/files/${encodeURIComponent(this.activeFile)}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content })
            });

            const data = await response.json();

            if (data.success) {
                // 更新文件状态
                const fileData = this.openFiles.get(this.activeFile);
                if (fileData) {
                    fileData.content = content;
                    fileData.modified = false;
                }
                this.updateEditorTabs();

                // 显示保存成功提示
                this.showNotification('文件保存成功', 'success');
            } else {
                console.error('Failed to save file:', data.error);
                alert('保存文件失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error saving file:', error);
            alert('保存文件时发生错误');
        }
    }

    markFileAsModified(path) {
        const fileData = this.openFiles.get(path);
        if (fileData && !fileData.modified) {
            fileData.modified = true;
            this.updateEditorTabs();
        }
    }

    closeFile(path) {
        const fileData = this.openFiles.get(path);
        if (fileData && fileData.modified) {
            if (!confirm('文件已修改，确定要关闭吗？未保存的更改将丢失。')) {
                return;
            }
        }

        this.openFiles.delete(path);

        if (this.activeFile === path) {
            // 切换到其他打开的文件或清空编辑器
            const remainingFiles = Array.from(this.openFiles.keys());
            if (remainingFiles.length > 0) {
                this.activeFile = remainingFiles[0];
                this.updateEditor();
            } else {
                this.activeFile = null;
                if (this.editor) {
                    this.editor.setValue('// 没有打开的文件');
                }
            }
        }

        this.updateEditorTabs();
    }

    async createNewFile() {
        const fileName = prompt('请输入文件名:');
        if (!fileName) return;

        try {
            const response = await fetch('/api/files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: fileName,
                    content: '',
                    type: 'file'
                })
            });

            const data = await response.json();

            if (data.success) {
                await this.loadFileTree();
                this.openFile(fileName);
            } else {
                console.error('Failed to create file:', data.error);
                alert('创建文件失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating file:', error);
            alert('创建文件时发生错误');
        }
    }

    async createNewFolder() {
        const folderName = prompt('请输入文件夹名:');
        if (!folderName) return;

        try {
            const response = await fetch('/api/files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: folderName,
                    type: 'folder'
                })
            });

            const data = await response.json();

            if (data.success) {
                await this.loadFileTree();
            } else {
                console.error('Failed to create folder:', data.error);
                alert('创建文件夹失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating folder:', error);
            alert('创建文件夹时发生错误');
        }
    }

    async runCurrentFile() {
        if (!this.activeFile) {
            alert('请先选择要运行的文件');
            return;
        }

        // 先保存文件
        await this.saveCurrentFile();

        try {
            const response = await fetch('/api/files/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: this.activeFile
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('代码运行成功', 'success');
                console.log('运行结果:', data.data);
            } else {
                console.error('Failed to run file:', data.error);
                alert('运行文件失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error running file:', error);
            alert('运行文件时发生错误');
        }
    }

    updateFileTreeSelection() {
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });

        if (this.activeFile) {
            const activeItem = document.querySelector(`[data-path="${this.activeFile}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }
    }

    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#28a745' : '#007bff'};
            color: white;
            border-radius: 6px;
            z-index: 1001;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new MinimalAgentApp();
});
